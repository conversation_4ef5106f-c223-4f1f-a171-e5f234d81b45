package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectCollaboratorService interface {
	Create(input *PMOCollaboratorCreatePayload) (*models.PMOCollaborator, core.IError)
	Update(id string, input *PMOCollaboratorUpdatePayload) (*models.PMOCollaborator, core.IError)
	Find(id string) (*models.PMOCollaborator, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *PMOCollaboratorPaginationOptions) (*repository.Pagination[models.PMOCollaborator], core.IError)
	Delete(id string) core.IError
}

type pmoProjectCollaboratorService struct {
	ctx core.IContext
}

func (s pmoProjectCollaboratorService) Create(input *PMOCollaboratorCreatePayload) (*models.PMOCollaborator, core.IError) {
	existing, ierr := repo.PMOCollaborator(s.ctx).FindOne("project_id = ? AND user_id = ?", input.ProjectID, input.UserID)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if existing != nil {
		return s.Update(existing.ID, &PMOCollaboratorUpdatePayload{
			ConfidentialPermission: input.ConfidentialPermission,
			ConfidentialMain:       input.ConfidentialMain,
			SalesPermission:        input.SalesPermission,
			SalesMain:              input.SalesMain,
			PresalesPermission:     input.PresalesPermission,
			PresalesMain:           input.PresalesMain,
			BiddingPermission:      input.BiddingPermission,
			BiddingMain:            input.BiddingMain,
			PMOPermission:          input.PMOPermission,
			PMOMain:                input.PMOMain,
		})
	}

	userSvc := NewUserService(s.ctx)
	user, ierr := userSvc.Find(input.UserID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if user.AccessLevel == nil || user.AccessLevel.Pmo == models.UserPermissionLevelNone {
		_, ierr := userSvc.UpdateAccessLevel(input.UserID, &UserAccessLevelUpdatePayload{
			Pmo: string(models.UserPermissionLevelUser),
		})
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	collaborator := &models.PMOCollaborator{
		BaseModel:              models.NewBaseModel(),
		ProjectID:              input.ProjectID,
		UserID:                 input.UserID,
		ConfidentialPermission: models.PMOTabPermissionNone,
		ConfidentialMain:       false,
		SalesPermission:        models.PMOTabPermissionNone,
		SalesMain:              false,
		PresalesPermission:     models.PMOTabPermissionNone,
		PresalesMain:           false,
		BiddingPermission:      models.PMOTabPermissionNone,
		BiddingMain:            false,
		PMOPermission:          models.PMOTabPermissionNone,
		PMOMain:                false,
		CreatedByID:            utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:            utils.ToPointer(s.ctx.GetUser().ID),
	}
	if input.ConfidentialPermission != nil {
		collaborator.ConfidentialPermission = utils.ToNonPointer(input.ConfidentialPermission)
	}
	if input.ConfidentialMain != nil {
		collaborator.ConfidentialMain = utils.ToNonPointer(input.ConfidentialMain)
	}
	if input.SalesPermission != nil {
		collaborator.SalesPermission = utils.ToNonPointer(input.SalesPermission)
	}
	if input.SalesMain != nil {
		collaborator.SalesMain = utils.ToNonPointer(input.SalesMain)
	}
	if input.PresalesPermission != nil {
		collaborator.PresalesPermission = utils.ToNonPointer(input.PresalesPermission)
	}
	if input.PresalesMain != nil {
		collaborator.PresalesMain = utils.ToNonPointer(input.PresalesMain)
	}
	if input.BiddingPermission != nil {
		collaborator.BiddingPermission = utils.ToNonPointer(input.BiddingPermission)
	}
	if input.BiddingMain != nil {
		collaborator.BiddingMain = utils.ToNonPointer(input.BiddingMain)
	}
	if input.PMOPermission != nil {
		collaborator.PMOPermission = utils.ToNonPointer(input.PMOPermission)
	}
	if input.PMOMain != nil {
		collaborator.PMOMain = utils.ToNonPointer(input.PMOMain)
	}
	
	ierr = repo.PMOCollaborator(s.ctx).Create(collaborator)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(collaborator.ID)
}

func (s pmoProjectCollaboratorService) Update(id string, input *PMOCollaboratorUpdatePayload) (*models.PMOCollaborator, core.IError) {
	collaborator, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	updateMap := map[string]interface{}{}
	if input.ConfidentialPermission != nil {
		updateMap["confidential_permission"] = input.ConfidentialPermission
	}
	if input.ConfidentialMain != nil {
		updateMap["confidential_main"] = input.ConfidentialMain
	}
	if input.SalesPermission != nil {
		updateMap["sales_permission"] = input.SalesPermission
	}
	if input.SalesMain != nil {
		updateMap["sales_main"] = input.SalesMain
	}
	if input.PresalesPermission != nil {
		updateMap["presales_permission"] = input.PresalesPermission
	}
	if input.PresalesMain != nil {
		updateMap["presales_main"] = input.PresalesMain
	}
	if input.BiddingPermission != nil {
		updateMap["bidding_permission"] = input.BiddingPermission
	}
	if input.BiddingMain != nil {
		updateMap["bidding_main"] = input.BiddingMain
	}
	if input.PMOPermission != nil {
		updateMap["pmo_permission"] = input.PMOPermission
	}
	if input.PMOMain != nil {
		updateMap["pmo_main"] = input.PMOMain
	}
	updateMap["updated_by_id"] = s.ctx.GetUser().ID

	ierr = repo.PMOCollaborator(s.ctx).Where("id = ?", id).Updates(updateMap)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(collaborator.ID)
}

func (s pmoProjectCollaboratorService) Find(id string) (*models.PMOCollaborator, core.IError) {
	return repo.PMOCollaborator(s.ctx,
		repo.PMOCollaboratorWithProject(),
		repo.PMOCollaboratorWithUser(),
	).FindOne("id = ?", id)
}

func (s pmoProjectCollaboratorService) Pagination(projectID string, pageOptions *core.PageOptions, options *PMOCollaboratorPaginationOptions) (*repository.Pagination[models.PMOCollaborator], core.IError) {
	return repo.PMOCollaborator(s.ctx,
		repo.PMOCollaboratorOrderBy(pageOptions),
		repo.PMOCollaboratorWithProject(),
		repo.PMOCollaboratorWithUser(),
		repo.PMOCollaboratorByProjectID(projectID),
		repo.PMOCollaboratorByTabKey(utils.ToNonPointer(options.TabKey)),
	).Pagination(pageOptions)
}

func (s pmoProjectCollaboratorService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.PMOCollaborator(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOProjectCollaboratorService(ctx core.IContext) IPMOProjectCollaboratorService {
	return &pmoProjectCollaboratorService{ctx: ctx}
}
