package handlers

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewAuthHTTP(e *echo.Echo) {
	auth := &AuthController{}

	e.POST("/auth/logout", core.WithHTTPContext(auth.Logout), middleware.AuthMiddleware())
	e.GET("/auth/slack-callback", core.WithHTTPContext(auth.SlackCallback))
	e.GET("/auth/slack-login", core.WithHTTPContext(auth.SlackLogin))
}
