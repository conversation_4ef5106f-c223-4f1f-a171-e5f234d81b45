package handlers

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/modules/ministry/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type MinistryController struct {
}

func (m MinistryController) Pagination(c core.IHTTPContext) error {
	ministrySvc := services.NewMinistryService(c)
	res, ierr := ministrySvc.Pagination(c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m MinistryController) Find(c core.IHTTPContext) error {
	ministrySvc := services.NewMinistryService(c)
	ministry, err := ministrySvc.Find(c.<PERSON>("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSO<PERSON>())
	}

	return c.<PERSON>SO<PERSON>(http.StatusOK, ministry)
}

func (m MinistryController) Create(c core.IHTTPContext) error {
	input := &requests.MinistryCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	ministrySvc := services.NewMinistryService(c)
	payload := &services.MinistryCreatePayload{}
	_ = utils.Copy(payload, input)
	ministry, err := ministrySvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, ministry)
}

func (m MinistryController) Update(c core.IHTTPContext) error {
	input := &requests.MinistryUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	ministrySvc := services.NewMinistryService(c)
	payload := &services.MinistryUpdatePayload{}
	_ = utils.Copy(payload, input)
	ministry, err := ministrySvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, ministry)
}

func (m MinistryController) Delete(c core.IHTTPContext) error {
	ministrySvc := services.NewMinistryService(c)
	err := ministrySvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
